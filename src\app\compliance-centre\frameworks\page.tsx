import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function FrameworksPage() {
  return (
    <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>NIST CSF 2.0</CardTitle>
            <CardDescription>NIST Cybersecurity Framework implementation</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Comprehensive NIST CSF 2.0 compliance management and assessment
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>ISO 27001</CardTitle>
            <CardDescription>Information Security Management System</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              ISO 27001 compliance tracking and certification support
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>PCI DSS</CardTitle>
            <CardDescription>Payment Card Industry Data Security Standard</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              PCI DSS compliance monitoring and validation
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>HIPAA</CardTitle>
            <CardDescription>Health Insurance Portability and Accountability Act</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              HIPAA Security Rule compliance and risk assessment
            </p>
          </CardContent>
        </Card>
    </div>
  )
}
