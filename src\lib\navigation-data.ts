import {
  Command,
  Eye,
  FileText,
  GalleryVerticalEnd,
  LayoutDashboard,
  Shield,
  ShieldCheck,
  Target,
  type LucideIcon,
} from "lucide-react"

export interface SubModule {
  title: string
  url: string
  description?: string
}

export interface NavigationItem {
  title: string
  url: string
  icon?: LucideIcon
  isActive?: boolean
  items?: {
    title: string
    url: string
    subModules?: SubModule[]
  }[]
}

export interface NavigationData {
  user: {
    name: string
    email: string
    avatar: string
  }
  teams: {
    name: string
    logo: React.ElementType
    plan: string
  }[]
  navMain: NavigationItem[]
}

// GRCOS navigation data based on GRC Ops Framework
export const navigationData: NavigationData = {
  user: {
    name: "GRC Admin",
    email: "<EMAIL>",
    avatar: "/avatars/admin.jpg",
  },
  teams: [
    {
      name: "Auris Compliance",
      logo: GalleryVerticalEnd,
      plan: "GRCOS Platform",
    },
    {
      name: "GRC Operations",
      logo: Shield,
      plan: "Framework",
    },
    {
      name: "Security Center",
      logo: Command,
      plan: "Active",
    },
  ],
  navMain: [
    {
      title: "Dashboard",
      url: "/dashboard",
      icon: LayoutDashboard,
      items: [
        {
          title: "Overview",
          url: "/dashboard/overview",
          subModules: [
            { title: "Executive Summary", url: "/dashboard/overview/executive", description: "High-level executive dashboard view" },
            { title: "Key Metrics", url: "/dashboard/overview/metrics", description: "Key performance indicators and metrics" },
            { title: "Risk Summary", url: "/dashboard/overview/risk", description: "Risk posture overview" },
            { title: "Compliance Status", url: "/dashboard/overview/compliance", description: "Overall compliance status" },
          ]
        },
        {
          title: "Activity",
          url: "/dashboard/activity",
          subModules: [
            { title: "Recent Events", url: "/dashboard/activity/events", description: "Recent security and compliance events" },
            { title: "User Activity", url: "/dashboard/activity/users", description: "User activity monitoring" },
            { title: "System Activity", url: "/dashboard/activity/system", description: "System and process activity" },
            { title: "Audit Trail", url: "/dashboard/activity/audit", description: "Comprehensive audit trail" },
          ]
        },
        {
          title: "Alerts",
          url: "/dashboard/alerts",
          subModules: [
            { title: "Critical Alerts", url: "/dashboard/alerts/critical", description: "High-priority security alerts" },
            { title: "Compliance Alerts", url: "/dashboard/alerts/compliance", description: "Compliance-related notifications" },
            { title: "System Alerts", url: "/dashboard/alerts/system", description: "System health and performance alerts" },
            { title: "Alert Management", url: "/dashboard/alerts/management", description: "Alert configuration and management" },
          ]
        },
        {
          title: "Compliance",
          url: "/dashboard/compliance",
          subModules: [
            { title: "Status Overview", url: "/dashboard/compliance/status", description: "Compliance status across frameworks" },
            { title: "Gap Analysis", url: "/dashboard/compliance/gaps", description: "Compliance gaps and remediation" },
            { title: "Progress Tracking", url: "/dashboard/compliance/progress", description: "Compliance improvement progress" },
            { title: "Reporting", url: "/dashboard/compliance/reporting", description: "Compliance reporting dashboard" },
          ]
        },
        {
          title: "Intelligence",
          url: "/dashboard/intelligence",
          subModules: [
            { title: "Threat Intelligence", url: "/dashboard/intelligence/threats", description: "Current threat landscape" },
            { title: "Risk Intelligence", url: "/dashboard/intelligence/risk", description: "Risk intelligence and analysis" },
            { title: "Market Intelligence", url: "/dashboard/intelligence/market", description: "Industry and market insights" },
            { title: "Predictive Analytics", url: "/dashboard/intelligence/predictive", description: "Predictive intelligence and forecasting" },
          ]
        },
        {
          title: "Actions",
          url: "/dashboard/actions",
          subModules: [
            { title: "Pending Actions", url: "/dashboard/actions/pending", description: "Actions requiring attention" },
            { title: "Completed Actions", url: "/dashboard/actions/completed", description: "Recently completed actions" },
            { title: "Scheduled Actions", url: "/dashboard/actions/scheduled", description: "Upcoming scheduled actions" },
            { title: "Action Analytics", url: "/dashboard/actions/analytics", description: "Action performance and analytics" },
          ]
        },
      ],
    },
    {
      title: "LightHouse",
      url: "/lighthouse",
      icon: Eye,
      items: [
        {
          title: "Monitor",
          url: "/lighthouse/monitor",
          subModules: [
            { title: "Dashboard", url: "/lighthouse/monitor/dashboard", description: "Live SIEM data from Wazuh integration" },
            { title: "Visualization", url: "/lighthouse/monitor/visualization", description: "Security event visualization" },
            { title: "Displays", url: "/lighthouse/monitor/displays", description: "Real-time monitoring displays" },
            { title: "Tracking", url: "/lighthouse/monitor/tracking", description: "Security metrics tracking" },
          ]
        },
        {
          title: "Detection",
          url: "/lighthouse/detection",
          subModules: [
            { title: "Behavior Analysis", url: "/lighthouse/detection/behavior", description: "AI-powered behavior analysis" },
            { title: "Anomaly Detection", url: "/lighthouse/detection/anomaly", description: "Unusual pattern identification" },
            { title: "Threat Hunting", url: "/lighthouse/detection/hunting", description: "Proactive threat hunting" },
            { title: "Intelligence", url: "/lighthouse/detection/intelligence", description: "Threat intelligence integration" },
          ]
        },
        {
          title: "Traffic",
          url: "/lighthouse/traffic",
          subModules: [
            { title: "Monitoring", url: "/lighthouse/traffic/monitoring", description: "IT/OT/IoT communication monitoring" },
            { title: "Analysis", url: "/lighthouse/traffic/analysis", description: "Traffic pattern analysis" },
            { title: "Flow Analysis", url: "/lighthouse/traffic/flow", description: "Network flow analysis" },
            { title: "Inspection", url: "/lighthouse/traffic/inspection", description: "Deep packet inspection" },
          ]
        },
        {
          title: "Management",
          url: "/lighthouse/management",
          subModules: [
            { title: "Alerts", url: "/lighthouse/management/alerts", description: "Prioritized security alerts" },
            { title: "Incidents", url: "/lighthouse/management/incidents", description: "Incident management" },
            { title: "Response", url: "/lighthouse/management/response", description: "Automated response actions" },
            { title: "Escalation", url: "/lighthouse/management/escalation", description: "Alert escalation workflows" },
          ]
        },
        {
          title: "Metrics",
          url: "/lighthouse/metrics",
          subModules: [
            { title: "System Health", url: "/lighthouse/metrics/health", description: "System health monitoring" },
            { title: "Performance", url: "/lighthouse/metrics/performance", description: "Security tool effectiveness" },
            { title: "Coverage", url: "/lighthouse/metrics/coverage", description: "Security coverage analysis" },
            { title: "Trends", url: "/lighthouse/metrics/trends", description: "Security trend analysis" },
          ]
        },
        {
          title: "Assets",
          url: "/lighthouse/assets",
          subModules: [
            { title: "Inventory", url: "/lighthouse/assets/inventory", description: "Comprehensive asset inventory management" },
            { title: "Verification", url: "/lighthouse/assets/verification", description: "Asset verification and validation" },
            { title: "Relationships", url: "/lighthouse/assets/relationships", description: "Asset relationship mapping" },
            { title: "Lifecycle", url: "/lighthouse/assets/lifecycle", description: "Asset lifecycle management" },
          ]
        },
        {
          title: "Identity",
          url: "/lighthouse/identity",
          subModules: [
            { title: "Management", url: "/lighthouse/identity/management", description: "Identity and access management" },
            { title: "Provisioning", url: "/lighthouse/identity/provisioning", description: "User provisioning and deprovisioning" },
            { title: "Authentication", url: "/lighthouse/identity/authentication", description: "Authentication systems management" },
            { title: "Authorization", url: "/lighthouse/identity/authorization", description: "Authorization and permissions" },
          ]
        },
        {
          title: "Threats",
          url: "/lighthouse/threats",
          subModules: [
            { title: "Feed", url: "/lighthouse/threats/feed", description: "Real-time threat data integration" },
            { title: "Surface", url: "/lighthouse/threats/surface", description: "Attack surface analysis" },
            { title: "Hunting", url: "/lighthouse/threats/hunting", description: "Proactive threat hunting" },
            { title: "Actors", url: "/lighthouse/threats/actors", description: "Threat actor intelligence" },
            { title: "Campaigns", url: "/lighthouse/threats/campaigns", description: "Threat campaign monitoring" },
          ]
        },
      ],
    },
    {
      title: "ComplianceCentre",
      url: "/compliance-centre",
      icon: ShieldCheck,
      items: [
        {
          title: "Frameworks",
          url: "/compliance-centre/frameworks",
          subModules: [
            { title: "NIST CSF 2.0", url: "/compliance-centre/frameworks/nist-csf", description: "NIST Cybersecurity Framework implementation" },
            { title: "ISO 27001", url: "/compliance-centre/frameworks/iso-27001", description: "Information Security Management System" },
            { title: "SOC 2", url: "/compliance-centre/frameworks/soc-2", description: "Service Organization Control 2 compliance" },
            { title: "PCI DSS", url: "/compliance-centre/frameworks/pci-dss", description: "Payment Card Industry Data Security Standard" },
          ]
        },
        {
          title: "Controls",
          url: "/compliance-centre/controls",
          subModules: [
            { title: "Implementation", url: "/compliance-centre/controls/implementation", description: "Control implementation tracking" },
            { title: "Testing", url: "/compliance-centre/controls/testing", description: "Automated control testing" },
            { title: "Effectiveness", url: "/compliance-centre/controls/effectiveness", description: "Control effectiveness measurement" },
            { title: "Mapping", url: "/compliance-centre/controls/mapping", description: "Cross-framework control mapping" },
          ]
        },
        {
          title: "Assessments",
          url: "/compliance-centre/assessments",
          subModules: [
            { title: "Planning", url: "/compliance-centre/assessments/planning", description: "Assessment planning and scheduling" },
            { title: "Execution", url: "/compliance-centre/assessments/execution", description: "Assessment execution and tracking" },
            { title: "Gap Analysis", url: "/compliance-centre/assessments/gap-analysis", description: "Compliance gap identification" },
            { title: "Remediation", url: "/compliance-centre/assessments/remediation", description: "Gap remediation planning" },
          ]
        },
        {
          title: "Reports",
          url: "/compliance-centre/reports",
          subModules: [
            { title: "Compliance", url: "/compliance-centre/reports/compliance", description: "Compliance status reporting" },
            { title: "Audit", url: "/compliance-centre/reports/audit", description: "Audit preparation and reports" },
            { title: "Executive", url: "/compliance-centre/reports/executive", description: "Executive summary reports" },
            { title: "Regulatory", url: "/compliance-centre/reports/regulatory", description: "Regulatory submission reports" },
          ]
        },
        {
          title: "Analytics",
          url: "/compliance-centre/analytics",
          subModules: [
            { title: "Risk Trends", url: "/compliance-centre/analytics/risk-trends", description: "Risk trend analysis" },
            { title: "Compliance Metrics", url: "/compliance-centre/analytics/compliance-metrics", description: "Compliance performance metrics" },
            { title: "Benchmarking", url: "/compliance-centre/analytics/benchmarking", description: "Industry benchmarking" },
            { title: "Predictions", url: "/compliance-centre/analytics/predictions", description: "Predictive compliance analytics" },
          ]
        },
      ],
    },
    {
      title: "ActionCentre",
      url: "/action-centre",
      icon: Target,
      items: [
        {
          title: "Workflows",
          url: "/action-centre/workflows",
          subModules: [
            { title: "Design", url: "/action-centre/workflows/design", description: "Workflow design and creation" },
            { title: "Execution", url: "/action-centre/workflows/execution", description: "Workflow execution and monitoring" },
            { title: "Templates", url: "/action-centre/workflows/templates", description: "Pre-built workflow templates" },
            { title: "Analytics", url: "/action-centre/workflows/analytics", description: "Workflow performance analytics" },
          ]
        },
        {
          title: "Remediation",
          url: "/action-centre/remediation",
          subModules: [
            { title: "Planning", url: "/action-centre/remediation/planning", description: "Remediation planning and prioritization" },
            { title: "Tracking", url: "/action-centre/remediation/tracking", description: "Remediation progress tracking" },
            { title: "Validation", url: "/action-centre/remediation/validation", description: "Remediation validation and testing" },
            { title: "Reporting", url: "/action-centre/remediation/reporting", description: "Remediation status reporting" },
          ]
        },
        {
          title: "Policies",
          url: "/action-centre/policies",
          subModules: [
            { title: "Management", url: "/action-centre/policies/management", description: "Policy creation and management" },
            { title: "Enforcement", url: "/action-centre/policies/enforcement", description: "Policy enforcement and monitoring" },
            { title: "Compliance", url: "/action-centre/policies/compliance", description: "Policy compliance tracking" },
            { title: "Updates", url: "/action-centre/policies/updates", description: "Policy updates and versioning" },
          ]
        },
        {
          title: "Automation",
          url: "/action-centre/automation",
          subModules: [
            { title: "Rules", url: "/action-centre/automation/rules", description: "Automation rules and triggers" },
            { title: "Scripts", url: "/action-centre/automation/scripts", description: "Automation scripts and playbooks" },
            { title: "Scheduling", url: "/action-centre/automation/scheduling", description: "Automated task scheduling" },
            { title: "Monitoring", url: "/action-centre/automation/monitoring", description: "Automation monitoring and logs" },
          ]
        },
        {
          title: "Incidents",
          url: "/action-centre/incidents",
          subModules: [
            { title: "Response", url: "/action-centre/incidents/response", description: "Incident response management" },
            { title: "Investigation", url: "/action-centre/incidents/investigation", description: "Incident investigation tools" },
            { title: "Communication", url: "/action-centre/incidents/communication", description: "Incident communication management" },
            { title: "Post-Mortem", url: "/action-centre/incidents/post-mortem", description: "Post-incident analysis" },
          ]
        },
      ],
    },
    {
      title: "TrustCentre",
      url: "/trust-centre",
      icon: FileText,
      items: [
        {
          title: "Reports",
          url: "/trust-centre/reports",
          subModules: [
            { title: "Audit Intelligence", url: "/trust-centre/reports/audit-intelligence", description: "Audit intelligence compilation" },
            { title: "Threat Reports", url: "/trust-centre/reports/threat-reports", description: "Threat report generation" },
            { title: "Compliance Reports", url: "/trust-centre/reports/compliance-reports", description: "Compliance status reports" },
            { title: "Executive Summaries", url: "/trust-centre/reports/executive-summaries", description: "Executive summary reports" },
          ]
        },
        {
          title: "Evidence",
          url: "/trust-centre/evidence",
          subModules: [
            { title: "Collection", url: "/trust-centre/evidence/collection", description: "Evidence collection and management" },
            { title: "Sharing", url: "/trust-centre/evidence/sharing", description: "Secure evidence sharing" },
            { title: "Distribution", url: "/trust-centre/evidence/distribution", description: "Evidence distribution management" },
            { title: "Archive", url: "/trust-centre/evidence/archive", description: "Evidence archival and retention" },
          ]
        },
        {
          title: "Communications",
          url: "/trust-centre/communications",
          subModules: [
            { title: "Stakeholder", url: "/trust-centre/communications/stakeholder", description: "Stakeholder communication management" },
            { title: "Tailored Reporting", url: "/trust-centre/communications/tailored-reporting", description: "Customized reporting for different audiences" },
            { title: "Notifications", url: "/trust-centre/communications/notifications", description: "Automated notification system" },
            { title: "Templates", url: "/trust-centre/communications/templates", description: "Communication templates" },
          ]
        },
        {
          title: "Dashboards",
          url: "/trust-centre/dashboards",
          subModules: [
            { title: "GRC Dashboard", url: "/trust-centre/dashboards/grc-dashboard", description: "Comprehensive GRC dashboard" },
            { title: "Visual Representation", url: "/trust-centre/dashboards/visual-representation", description: "Visual data representation" },
            { title: "Custom Views", url: "/trust-centre/dashboards/custom-views", description: "Customizable dashboard views" },
            { title: "Real-time Metrics", url: "/trust-centre/dashboards/real-time-metrics", description: "Real-time GRC metrics" },
          ]
        },
        {
          title: "TPRM",
          url: "/trust-centre/tprm",
          subModules: [
            { title: "Vendor Assessment", url: "/trust-centre/tprm/vendor-assessment", description: "Third-party vendor assessment" },
            { title: "Risk Management", url: "/trust-centre/tprm/risk-management", description: "Third-party risk management" },
            { title: "Monitoring", url: "/trust-centre/tprm/monitoring", description: "Continuous vendor monitoring" },
            { title: "Contracts", url: "/trust-centre/tprm/contracts", description: "Vendor contract management" },
          ]
        },
      ],
    },
  ],
}
