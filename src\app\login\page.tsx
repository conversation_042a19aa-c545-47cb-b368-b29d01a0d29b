import { SquaresFour } from "@phosphor-icons/react"

import { LoginForm } from "@/components/login-form"
import { ThemeToggle } from "@/components/theme-toggle"
import Image from "next/image"

export default function LoginPage() {
  return (
    <div className="grid min-h-svh lg:grid-cols-2">
      <div className="flex flex-col gap-4 p-6 md:p-10">
        <div className="flex justify-between items-center">
          <div className="flex justify-center gap-2 md:justify-start">
            <a href="#" className="flex items-center gap-2 font-medium">
              <div className="flex h-6 w-6 items-center justify-center rounded-md bg-gray-900 text-white dark:bg-gray-100 dark:text-gray-900">
                <SquaresFour className="size-4" />
              </div>
              Auris Compliance
            </a>
          </div>
          <ThemeToggle />
        </div>
        <div className="flex flex-1 items-center justify-center">
          <div className="w-full max-w-xs">
            <LoginForm />
          </div>
        </div>
      </div>
      <div className="relative hidden lg:block overflow-hidden">
        {/* Background Image */}
        <Image
          src="/and-machines-6Hjnj9fTZvc-unsplash.jpg"
          alt="Background"
          className="absolute inset-0 h-full w-full object-cover"
        />

        {/* Content */}
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-center text-white max-w-lg px-8">
            <div className="mb-6 flex justify-center">
              <div className="flex h-20 w-20 items-center justify-center rounded-3xl bg-white/10 backdrop-blur-sm border border-white/20">
                <SquaresFour className="h-10 w-10" />
              </div>
            </div>
            <div className="mb-2">
              <h1 className="text-4xl font-bold mb-1">Auris Compliance</h1>
              <div className="text-gray-200 text-xl font-medium">GRCOS Platform</div>
            </div>
            <p className="text-gray-100 text-lg leading-relaxed">
              GRC Orchestration System for comprehensive governance, risk, and compliance management across your organization
            </p>
            <div className="mt-8 flex justify-center space-x-8 text-gray-200">
              <div className="text-center">
                <div className="text-2xl font-bold">99%</div>
                <div className="text-sm">Compliance Rate</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">24/7</div>
                <div className="text-sm">Monitoring</div>
              </div>
              <div className="text-center">
                <div className="text-2xl font-bold">AI</div>
                <div className="text-sm">Powered</div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Gradient */}
        <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-black/40 to-transparent dark:from-black/60 dark:to-transparent"></div>
      </div>
    </div>
  )
}
