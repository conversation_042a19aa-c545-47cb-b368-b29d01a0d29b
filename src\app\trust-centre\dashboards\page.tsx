import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

export default function DashboardsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Dashboards</h2>
        <p className="text-muted-foreground">
          GRC dashboard creation and visual representation of security posture
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Interactive</CardTitle>
            <CardDescription>Plotly/Dash interactive dashboards</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Interactive dashboards built with <PERSON>lot<PERSON> and Dash
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Executive</CardTitle>
            <CardDescription>Executive-level dashboards</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              High-level executive dashboards for strategic decision making
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Operational</CardTitle>
            <CardDescription>Operational security dashboards</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Real-time operational dashboards for security teams
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Compliance</CardTitle>
            <CardDescription>Compliance status dashboards</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Visual representation of compliance status across frameworks
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
