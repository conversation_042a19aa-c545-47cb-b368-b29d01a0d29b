import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { FileText, Shield, ChartBar, ClipboardText, TrendUp } from "@phosphor-icons/react"

export default function ComplianceCentrePage() {
  const modules = [
    {
      title: "Frameworks",
      description: "Multi-framework compliance mapping and management",
      icon: Shield,
      href: "/compliance-centre/frameworks",
    },
    {
      title: "Controls",
      description: "Security control implementation and monitoring",
      icon: ClipboardText,
      href: "/compliance-centre/controls",
    },
    {
      title: "Assessments",
      description: "Automated compliance assessments and gap analysis",
      icon: ChartBar,
      href: "/compliance-centre/assessments",
    },
    {
      title: "Reports",
      description: "Compliance reporting and documentation",
      icon: FileText,
      href: "/compliance-centre/reports",
    },
    {
      title: "Analytics",
      description: "Risk analytics and compliance trend analysis",
      icon: TrendUp,
      href: "/compliance-centre/analytics",
    },
  ]

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {modules.map((module) => (
        <Card key={module.title} className="cursor-pointer hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <module.icon className="h-5 w-5" />
              <CardTitle>{module.title}</CardTitle>
            </div>
            <CardDescription>{module.description}</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Click to access {module.title.toLowerCase()} functionality
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
