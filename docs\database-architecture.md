# GRCOS Unified Data Storage Architecture

## Overview

This document outlines the comprehensive data storage strategy for GRCOS, integrating core platform requirements with Wazuh security data and CrewAI orchestration capabilities.

## Core Database Components

### PostgreSQL (Primary Relational Database)

**Purpose:** Structured data storage for GRC operations, workflows, and configurations

**Key Data Elements:**
- Asset inventory and relationships
- Compliance frameworks and controls
- Control mappings across frameworks
- Risk assessments and scoring
- Remediation plans and tasks
- Workflow definitions and instances
- Report configurations and schedules
- User and stakeholder information

**Technical Benefits:**
- ACID compliance for transactional integrity
- Row-level security for multi-tenancy
- JSON support for semi-structured data
- Strong integration with Python ecosystem
- Open-source with enterprise features

### MongoDB (Document Store)

**Purpose:** Storage for unstructured evidence and compliance artifacts

**Key Data Elements:**
- Raw compliance evidence
- Security scan results
- Policy and procedure documents
- Assessment artifacts
- Report outputs and evidence packages

**Technical Benefits:**
- Flexible schema for varying evidence types
- Efficient storage of document-oriented data
- Horizontal scaling for large evidence repositories
- Strong query capabilities for complex documents

### Elasticsearch (Security Event Store)

**Purpose:** Integration with Wazuh for security monitoring data

**Key Data Elements:**
- Security events and alerts
- Compliance scan results
- File integrity monitoring data
- Vulnerability assessment results
- Agent status information

**Technical Benefits:**
- Optimized for time-series security data
- Powerful full-text search capabilities
- Visualization integration with Kibana
- Scalable for high-volume security events

### Redis (Caching Layer)

**Purpose:** Performance optimization and real-time features

**Key Data Elements:**
- Dashboard data caches
- Task queues
- Session management
- Real-time notifications

**Technical Benefits:**
- Ultra-fast in-memory operations
- Pub/sub for real-time features
- Simple implementation for caching
- Low operational overhead

## Domain-Specific Data Storage

### Integration Hub (Aggregation Kernel)

**Primary Data:**
- Asset inventory (PostgreSQL)
- Data source configurations (PostgreSQL)
- Raw security events (Elasticsearch via Wazuh)
- Evidence collection history (PostgreSQL)
- Raw evidence artifacts (MongoDB)

**Data Flow:**
1. Wazuh collects security data into Elasticsearch
2. GRCOS extracts, normalizes, and enriches this data
3. Processed data flows to PostgreSQL for GRC operations
4. Raw evidence stored in MongoDB for compliance purposes

### Compliance Centre (Analysis Kernel)

**Primary Data:**
- Compliance frameworks and controls (PostgreSQL)
- Cross-framework mappings (PostgreSQL)
- Risk assessments and calculations (PostgreSQL)
- Compliance status tracking (PostgreSQL)
- Assessment artifacts (MongoDB)

**Data Flow:**
1. Framework definitions stored as structured data
2. Control mappings enable multi-framework compliance
3. Risk data calculated and stored for reporting
4. Supporting documentation maintained in document store

### Action Centre (Remediation Kernel)

**Primary Data:**
- Remediation plans and tasks (PostgreSQL)
- Workflow definitions (Flowable tables in PostgreSQL)
- Workflow instances (Flowable tables in PostgreSQL)
- Policies and procedures (PostgreSQL/MongoDB)
- Remediation evidence (MongoDB)

**Data Flow:**
1. Compliance gaps trigger remediation planning
2. Workflows orchestrate remediation activities
3. Task status tracked in relational database
4. Evidence of remediation stored for audit purposes

### Trust Centre (Reporting Kernel)

**Primary Data:**
- Report definitions (PostgreSQL)
- Dashboard configurations (PostgreSQL)
- Generated reports (MongoDB)
- Evidence packages (MongoDB)
- Visualization data caches (Redis)

**Data Flow:**
1. Report templates defined in structured database
2. Data aggregated from multiple sources for reporting
3. Generated reports stored as documents
4. Dashboards cached for performance

## Wazuh Integration Details

### Data Sources in Wazuh

- **Elasticsearch:** Security events, alerts, and monitoring data
- **Wazuh Database:** Agent management and configuration (SQLite/PostgreSQL)
- **File System:** Raw logs, configurations, and rules

### Integration Approach

1. **Direct Elasticsearch Access:**
   - GRCOS connects to Wazuh's Elasticsearch
   - Custom indices created for GRCOS-specific data
   - ETL processes transform security data for GRC context

2. **Wazuh API Integration:**
   - GRCOS uses Wazuh API for real-time data
   - Configuration management through API calls
   - Agent management via programmatic interface

3. **Shared PostgreSQL Option:**
   - Configure Wazuh to use PostgreSQL instead of SQLite
   - Share database instance with schema separation
   - Direct database queries for efficient data access

## CrewAI Integration Details

### Data Requirements for CrewAI

- **Agent Configurations:** Roles, capabilities, and tools
- **Task Definitions:** Process workflows and dependencies
- **Execution State:** Runtime status and outputs
- **Knowledge Base:** Reference materials for agents

### Storage Implementation

1. **Configuration Management:**
   - YAML files in version control for static definitions
   - PostgreSQL tables for runtime configurations
   - MongoDB for unstructured knowledge documents

2. **Execution Tracking:**
   - `crew_executions`: Overall process executions
   - `agent_tasks`: Individual agent activities
   - `agent_outputs`: Results and artifacts
   - `execution_logs`: Detailed execution records

3. **Knowledge Management:**
   - Structured knowledge in PostgreSQL
   - Document-based knowledge in MongoDB
   - Optional vector embeddings for semantic search

## Data Flows and Integration

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│  Security Data  │     │   GRC Platform  │     │  Orchestration  │
│    (Wazuh)      │────▶│     (GRCOS)     │◀────│    (CrewAI)     │
└────────┬────────┘     └────────┬────────┘     └────────┬────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────────────────────────────────────────────────────┐
│                     Unified Data Storage                         │
├─────────────┬─────────────┬─────────────────┬─────────────────┐
│ PostgreSQL  │  MongoDB    │  Elasticsearch  │     Redis       │
│ (Structured │ (Documents) │  (Security      │  (Caching &     │
│   Data)     │             │   Events)       │   Real-time)    │
└─────────────┴─────────────┴─────────────────┴─────────────────┘
```

## Implementation Recommendations

### 1. Database Setup and Configuration

- Deploy PostgreSQL with appropriate extensions (e.g., pgcrypto)
- Configure MongoDB with replica set for reliability
- Implement proper authentication and encryption
- Set up Redis with persistence for critical data

### 2. Data Migration and Versioning

- Implement database migrations using Alembic
- Version control schema definitions
- Create data migration utilities for framework updates

### 3. Performance Optimization

- Implement appropriate indexing strategies
- Use materialized views for complex analytics
- Configure Redis caching for frequently accessed data
- Consider read replicas for reporting workloads

### 4. Security and Compliance

- Implement transparent data encryption
- Configure comprehensive audit logging
- Set up automated backup and recovery processes
- Implement data retention policies aligned with compliance requirements

### 5. Scalability Planning

- Design for horizontal scaling of all components
- Implement proper sharding strategies
- Consider managed database services to reduce operational overhead

## Conclusion

This unified data storage architecture provides GRCOS with a robust foundation that effectively integrates security monitoring data from Wazuh and orchestration capabilities from CrewAI while maintaining the structured approach needed for comprehensive GRC operations. The hybrid approach balances relational integrity for compliance tracking with the flexibility needed for unstructured evidence and the performance required for user-facing features.