"use client"

import { ChevronRight, type LucideIcon } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"

import {
  Collapsible,
  CollapsibleContent,
  CollapsibleTrigger,
} from "@/components/ui/collapsible"
import {
  SidebarGroup,
  SidebarGroupLabel,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubButton,
  SidebarMenuSubItem,
} from "@/components/ui/sidebar"

import { NavigationItem } from "@/lib/navigation-data"

export function NavMain({
  items,
}: {
  items: NavigationItem[]
}) {
  const pathname = usePathname()

  return (
    <SidebarGroup>
      <SidebarGroupLabel>Platform</SidebarGroupLabel>
      <SidebarMenu>
        {items.map((item) => {
          const isMainActive = pathname === item.url || pathname.startsWith(item.url + "/")

          // Check if any sub-item or its sub-modules are active
          const hasActiveSubItem = item.items?.some(subItem => {
            // Check if the sub-item itself is active
            if (pathname === subItem.url || pathname.startsWith(subItem.url + "/")) {
              return true
            }
            // Check if any of the sub-modules are active
            return subItem.subModules?.some(subModule => pathname === subModule.url)
          })

          const shouldBeOpen = item.isActive || isMainActive || hasActiveSubItem

          return (
            <Collapsible
              key={item.title}
              asChild
              defaultOpen={shouldBeOpen}
              className="group/collapsible"
            >
              <SidebarMenuItem>
                <CollapsibleTrigger asChild>
                  <SidebarMenuButton
                    tooltip={item.title}
                    className={cn(
                      "transition-all duration-200 relative",
                      "hover:bg-gray-50 dark:hover:bg-gray-800/30",
                      isMainActive && "bg-gray-100 text-gray-900 hover:bg-gray-150 dark:bg-gray-800/60 dark:text-gray-100 dark:hover:bg-gray-800/80 border-l-2 border-gray-400 dark:border-gray-500"
                    )}
                  >
                    {item.icon && (
                      <item.icon className={cn(
                        "transition-colors duration-200",
                        isMainActive && "text-gray-700 dark:text-gray-300"
                      )} />
                    )}
                    <span>{item.title}</span>
                    <ChevronRight className={cn(
                      "ml-auto transition-all duration-200 group-data-[state=open]/collapsible:rotate-90",
                      isMainActive && "text-gray-700 dark:text-gray-300"
                    )} />
                  </SidebarMenuButton>
                </CollapsibleTrigger>
                <CollapsibleContent>
                  <SidebarMenuSub>
                    {item.items?.map((subItem) => {
                      // Check if this sub-item or any of its sub-modules are active
                      const isSubActive = pathname === subItem.url || pathname.startsWith(subItem.url + "/") ||
                        subItem.subModules?.some(subModule => pathname === subModule.url)

                      return (
                        <SidebarMenuSubItem key={subItem.title}>
                          <SidebarMenuSubButton
                            asChild
                            className={cn(
                              "transition-all duration-200 relative",
                              "hover:bg-gray-50 dark:hover:bg-gray-800/30",
                              isSubActive && "bg-gray-100 text-gray-900 hover:bg-gray-150 dark:bg-gray-800/60 dark:text-gray-100 dark:hover:bg-gray-800/80 border-l-2 border-gray-400 dark:border-gray-500 ml-2"
                            )}
                          >
                            <Link href={subItem.url}>
                              <span className={cn(
                                "transition-colors duration-200",
                                isSubActive && "font-medium"
                              )}>
                                {subItem.title}
                              </span>
                            </Link>
                          </SidebarMenuSubButton>
                        </SidebarMenuSubItem>
                      )
                    })}
                  </SidebarMenuSub>
                </CollapsibleContent>
              </SidebarMenuItem>
            </Collapsible>
          )
        })}
      </SidebarMenu>
    </SidebarGroup>
  )
}
