import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Identity - Identity & Access Management | GRCOS",
  description: "Comprehensive identity and access management with AI-driven risk assessment",
}

export default function IdentityLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="flex flex-col space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">Identity</h1>
        <p className="text-muted-foreground">
          Comprehensive identity and access management with AI-driven risk assessment
        </p>
      </div>
      {children}
    </div>
  )
}
