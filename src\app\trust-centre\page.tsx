import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { FileText, Database, ChatCircle, ChartBar, Buildings } from "@phosphor-icons/react"

export default function TrustCentrePage() {
  const modules = [
    {
      title: "Reports",
      description: "Audit intelligence compilation and threat report generation",
      icon: FileText,
      href: "/trust-centre/reports",
    },
    {
      title: "Evidence",
      description: "Evidence sharing and secure distribution of compliance artifacts",
      icon: Database,
      href: "/trust-centre/evidence",
    },
    {
      title: "Communications",
      description: "Stakeholder communication and tailored reporting",
      icon: ChatCircle,
      href: "/trust-centre/communications",
    },
    {
      title: "Dashboards",
      description: "GRC dashboard creation and visual representation",
      icon: ChartBar,
      href: "/trust-centre/dashboards",
    },
    {
      title: "TPRM",
      description: "Third-party risk management and vendor assessment",
      icon: Buildings,
      href: "/trust-centre/tprm",
    },
  ]

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {modules.map((module) => (
        <Card key={module.title} className="cursor-pointer hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <module.icon className="h-5 w-5" />
              <CardTitle>{module.title}</CardTitle>
            </div>
            <CardDescription>{module.description}</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Click to access {module.title.toLowerCase()} functionality
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
