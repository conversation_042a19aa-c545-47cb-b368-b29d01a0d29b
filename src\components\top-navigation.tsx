"use client"

import * as React from "react"
import Link from "next/link"
import { usePathname, useRouter } from "next/navigation"
import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { navigationData } from "@/lib/navigation-data"

export function TopNavigation() {
  const pathname = usePathname()
  const router = useRouter()

  // Find the current active sub-item and its sub-modules
  const { activeSubItem, subModules } = React.useMemo(() => {
    for (const mainItem of navigationData.navMain) {
      if (pathname === mainItem.url || pathname.startsWith(mainItem.url + "/")) {
        // Find the specific sub-item (e.g., /compliance-centre/controls)
        const subItem = mainItem.items?.find(item =>
          pathname === item.url || pathname.startsWith(item.url + "/")
        )

        if (subItem && subItem.subModules) {
          return { activeSubItem: subItem, subModules: subItem.subModules }
        }
      }
    }
    return { activeSubItem: null, subModules: null }
  }, [pathname])

  // Auto-navigate to first sub-module when sub-item is accessed directly
  React.useEffect(() => {
    if (activeSubItem && subModules && pathname === activeSubItem.url && subModules.length > 0) {
      const firstSubModule = subModules[0]
      if (firstSubModule && firstSubModule.url !== activeSubItem.url) {
        router.replace(firstSubModule.url)
      }
    }
  }, [activeSubItem, subModules, pathname, router])

  // Don't render if no active sub-item or no sub-modules
  if (!activeSubItem || !subModules || subModules.length === 0) {
    return null
  }

  return (
    <nav className="flex items-center gap-2">
      {subModules.map((subModule) => {
        const isActive = pathname === subModule.url

        return (
          <Button
            key={subModule.url}
            variant="ghost"
            size="sm"
            asChild
            className={cn(
              "h-8 px-4 text-sm font-medium transition-all duration-200",
              "hover:bg-gray-50 dark:hover:bg-gray-800/30",
              "text-gray-600 dark:text-gray-400",
              isActive && [
                "bg-gray-100 text-gray-900 hover:bg-gray-150",
                "dark:bg-gray-800/60 dark:text-gray-100 dark:hover:bg-gray-800/80",
                "border-b-2 border-gray-400 dark:border-gray-500 rounded-b-none"
              ]
            )}
          >
            <Link href={subModule.url}>
              {subModule.title}
            </Link>
          </Button>
        )
      })}
    </nav>
  )
}
