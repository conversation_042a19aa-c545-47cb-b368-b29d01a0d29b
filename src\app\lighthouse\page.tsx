import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Eye, Activity, Network, BarChart3, Settings } from "lucide-react"

export default function LightHousePage() {
  const modules = [
    {
      title: "Monitor",
      description: "Live SIEM data from Wazuh integration and real-time security monitoring",
      icon: Eye,
      href: "/lighthouse/monitor",
    },
    {
      title: "Detection",
      description: "AI-powered behavior analysis and unusual pattern identification",
      icon: Activity,
      href: "/lighthouse/detection",
    },
    {
      title: "Traffic",
      description: "IT/OT/IoT communication monitoring and network flow analysis",
      icon: Network,
      href: "/lighthouse/traffic",
    },
    {
      title: "Management",
      description: "Prioritized security alerts with contextual information",
      icon: Settings,
      href: "/lighthouse/management",
    },
    {
      title: "Metrics",
      description: "System health monitoring and security tool effectiveness",
      icon: BarChart3,
      href: "/lighthouse/metrics",
    },
  ]

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {modules.map((module) => (
        <Card key={module.title} className="cursor-pointer hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <module.icon className="h-5 w-5" />
              <CardTitle>{module.title}</CardTitle>
            </div>
            <CardDescription>{module.description}</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Click to access {module.title.toLowerCase()} functionality
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
