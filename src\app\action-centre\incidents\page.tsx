import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"

export default function IncidentsPage() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Incidents</h2>
        <p className="text-muted-foreground">
          Incident response and management
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Response</CardTitle>
            <CardDescription>DFIR-IRIS incident response</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Comprehensive incident response using DFIR-IRIS platform
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Management</CardTitle>
            <CardDescription>Incident lifecycle management</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              End-to-end incident management and tracking
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Investigation</CardTitle>
            <CardDescription>Digital forensics and investigation</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Advanced digital forensics and incident investigation tools
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Recovery</CardTitle>
            <CardDescription>Incident recovery and lessons learned</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Post-incident recovery and continuous improvement processes
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
