import {
  // Navigation icons
  House,
  Eye,
  FileText,
  Shield,
  ShieldCheck,
  Target,
  Command,
  SquaresFour,
  
  // User interface icons
  CaretUpDown,
  CaretRight,
  Bell,
  CreditCard,
  SignOut,
  Sparkle,
  Check,
  Circle,
  DotsThreeOutline,
  Folder,
  ArrowRight,
  Trash,
  Plus,
  List,
  
  // Dashboard icons
  ChartBar,
  Activity,
  Warning,
  Brain,
  Lightning,
  Gear,
  Network,
  
  // Sidebar icons
  Sidebar,
  
  // Type for Phosphor icons
  Icon as PhosphorIcon,
} from "@phosphor-icons/react";

// Export the PhosphorIcon type for use in other components
export type { PhosphorIcon };

// Icon mapping from Lucide names to Phosphor components
export const iconMap = {
  // Navigation
  LayoutDashboard: House,
  Eye: Eye,
  FileText: FileText,
  Shield: Shield,
  ShieldCheck: ShieldCheck,
  Target: Target,
  Command: Command,
  GalleryVerticalEnd: SquaresFour,
  
  // User interface
  ChevronsUpDown: CaretUpDown,
  ChevronRight: CaretRight,
  Bell: Bell,
  CreditCard: CreditCard,
  LogOut: SignOut,
  Sparkles: Sparkle,
  BadgeCheck: ShieldCheck,
  Check: Check,
  Circle: Circle,
  MoreHorizontal: DotsThreeOutline,
  Folder: Folder,
  Forward: ArrowRight,
  Trash2: Trash,
  Plus: Plus,
  PanelLeft: Sidebar,
  
  // Dashboard
  BarChart3: ChartBar,
  Activity: Activity,
  AlertTriangle: Warning,
  Brain: Brain,
  Zap: Lightning,
  Settings: Gear,
  Network: Network,
} as const;

// Helper function to get Phosphor icon from Lucide name
export function getPhosphorIcon(lucideName: keyof typeof iconMap): PhosphorIcon {
  return iconMap[lucideName];
}

// Default icon props for consistent styling
export const defaultIconProps = {
  size: 16,
  weight: "regular" as const,
};

// Icon variants for different use cases
export const iconVariants = {
  navigation: {
    size: 16,
    weight: "regular" as const,
  },
  button: {
    size: 16,
    weight: "regular" as const,
  },
  large: {
    size: 24,
    weight: "regular" as const,
  },
  small: {
    size: 14,
    weight: "regular" as const,
  },
} as const;
