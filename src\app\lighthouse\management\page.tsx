import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"

export default function ManagementPage() {
  return (
    <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Prioritization</CardTitle>
            <CardDescription>Prioritized security alerts</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Risk-based alert prioritization and triage
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Context</CardTitle>
            <CardDescription>Contextual alert information</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Enriched alerts with business and technical context
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Correlation</CardTitle>
            <CardDescription>Alert correlation engine</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Intelligent correlation of related security events
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Response</CardTitle>
            <CardDescription>Alert response tracking</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Comprehensive tracking of alert response activities
            </p>
          </CardContent>
        </Card>
    </div>
  )
}
