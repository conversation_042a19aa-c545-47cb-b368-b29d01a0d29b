import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { ChartBar, Activity, Warning, Shield, Brain, Lightning } from "@phosphor-icons/react"

export default function DashboardPage() {
  const dashboardModules = [
    {
      title: "Overview",
      description: "Real-time security score, compliance status, and executive metrics",
      icon: ChartBar,
      href: "/dashboard",
      stats: "98% Security Score",
    },
    {
      title: "Activity",
      description: "Live updates from CrewAI agents and cross-domain activities",
      icon: Activity,
      href: "/dashboard/activity",
      stats: "24 Active Agents",
    },
    {
      title: "Alerts",
      description: "Priority notification system with severity classification",
      icon: Warning,
      href: "/dashboard/alerts",
      stats: "3 High Priority",
    },
    {
      title: "Compliance",
      description: "Multi-framework compliance percentages and deadlines",
      icon: Shield,
      href: "/dashboard/compliance",
      stats: "94% Compliant",
    },
    {
      title: "Intelligence",
      description: "Current threat landscape and organization-relevant threats",
      icon: Brain,
      href: "/dashboard/intelligence",
      stats: "12 New Threats",
    },
    {
      title: "Actions",
      description: "One-click task access and cross-domain shortcuts",
      icon: Lightning,
      href: "/dashboard/actions",
      stats: "8 Pending",
    },
  ]

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold tracking-tight">Dashboard</h1>
        <p className="text-muted-foreground">
          Executive command center for GRCOS operations and insights
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {dashboardModules.map((module) => (
          <Card key={module.title} className="cursor-pointer hover:shadow-lg hover:bg-gray-50 dark:hover:bg-gray-800/50 transition-all duration-200">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <module.icon className="h-5 w-5" />
                  <CardTitle>{module.title}</CardTitle>
                </div>
                <div className="text-sm font-medium text-gray-600 dark:text-gray-400">
                  {module.stats}
                </div>
              </div>
              <CardDescription>{module.description}</CardDescription>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground">
                Click to access {module.title.toLowerCase()} dashboard
              </p>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}
