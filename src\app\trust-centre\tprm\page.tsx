import { Card, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"

export default function TPRMPage() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">TPRM</h2>
        <p className="text-muted-foreground">
          Third-party risk management and vendor risk assessment reporting
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Assessment</CardTitle>
            <CardDescription>Vendor risk assessment</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Comprehensive third-party risk assessment and evaluation
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Monitoring</CardTitle>
            <CardDescription>Continuous vendor monitoring</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Ongoing monitoring of third-party security posture
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Reporting</CardTitle>
            <CardDescription>TPRM reporting and analytics</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Comprehensive reporting on third-party risk landscape
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>OSINT</CardTitle>
            <CardDescription>Open source intelligence collection</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              OSINT collection for third-party risk assessment
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
