import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"

export default function DetectionPage() {
  return (
    <div className="grid gap-6 md:grid-cols-2">
      <Card>
        <CardHeader>
          <CardTitle>Behavior Analysis</CardTitle>
          <CardDescription>AI-powered behavior analysis</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Advanced behavioral analytics for threat detection
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Anomaly Detection</CardTitle>
          <CardDescription>Unusual pattern identification</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Automated identification of anomalous activities
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Threat Hunting</CardTitle>
          <CardDescription>Proactive threat hunting</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Proactive threat hunting and investigation capabilities
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Intelligence</CardTitle>
          <CardDescription>Threat intelligence integration</CardDescription>
        </CardHeader>
        <CardContent>
          <p className="text-sm text-muted-foreground">
            Integration with threat intelligence feeds and analysis
          </p>
        </CardContent>
      </Card>
    </div>
  )
}
