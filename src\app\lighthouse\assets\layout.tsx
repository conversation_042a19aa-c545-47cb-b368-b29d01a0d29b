import { Metadata } from "next"

export const metadata: Metadata = {
  title: "Assets - Asset Management | GRCOS",
  description: "Comprehensive asset inventory, verification, and lifecycle management",
}

export default function AssetsLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="flex flex-col space-y-6">
      <div className="border-b pb-4">
        <h1 className="text-3xl font-bold tracking-tight">Assets</h1>
        <p className="text-muted-foreground">
          Comprehensive asset inventory, verification, and lifecycle management
        </p>
      </div>
      {children}
    </div>
  )
}
