import { Card, CardContent, CardDescription, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card"
import { Workflow, Wrench, FileText, Zap, AlertCircle } from "lucide-react"

export default function ActionCentrePage() {
  const modules = [
    {
      title: "Workflows",
      description: "Automated workflow orchestration and process management",
      icon: Workflow,
      href: "/action-centre/workflows",
    },
    {
      title: "Remediation",
      description: "Security remediation planning and execution",
      icon: Wrench,
      href: "/action-centre/remediation",
    },
    {
      title: "Policies",
      description: "Policy management and enforcement automation",
      icon: FileText,
      href: "/action-centre/policies",
    },
    {
      title: "Automation",
      description: "Security automation and orchestration",
      icon: Zap,
      href: "/action-centre/automation",
    },
    {
      title: "Incidents",
      description: "Incident response and management",
      icon: AlertCircle,
      href: "/action-centre/incidents",
    },
  ]

  return (
    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
      {modules.map((module) => (
        <Card key={module.title} className="cursor-pointer hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-center space-x-2">
              <module.icon className="h-5 w-5" />
              <CardTitle>{module.title}</CardTitle>
            </div>
            <CardDescription>{module.description}</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Click to access {module.title.toLowerCase()} functionality
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
