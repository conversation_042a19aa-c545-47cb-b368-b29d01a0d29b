import { Card, CardContent, CardDescription, Card<PERSON>eader, CardTitle } from "@/components/ui/card"

export default function EvidencePage() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold tracking-tight">Evidence</h2>
        <p className="text-muted-foreground">
          Evidence sharing and secure distribution of compliance artifacts
        </p>
      </div>
      
      <div className="grid gap-6 md:grid-cols-2">
        <Card>
          <CardHeader>
            <CardTitle>Collection</CardTitle>
            <CardDescription>Automated evidence collection</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Automated collection of compliance evidence from systems
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Management</CardTitle>
            <CardDescription>Evidence lifecycle management</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Comprehensive management of evidence throughout its lifecycle
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Sharing</CardTitle>
            <CardDescription>Secure evidence sharing portal</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Secure distribution and sharing of compliance evidence
            </p>
          </CardContent>
        </Card>
        
        <Card>
          <CardHeader>
            <CardTitle>Verification</CardTitle>
            <CardDescription>Evidence integrity verification</CardDescription>
          </CardHeader>
          <CardContent>
            <p className="text-sm text-muted-foreground">
              Blockchain-based verification of evidence integrity
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
