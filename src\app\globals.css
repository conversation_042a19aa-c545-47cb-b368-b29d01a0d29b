@tailwind base;
@tailwind components;
@tailwind utilities;



@layer base {
  :root {

    --background: 0 0% 100%;

    --foreground: 0 0% 5%;

    --card: 0 0% 100%;

    --card-foreground: 0 0% 5%;

    --popover: 0 0% 100%;

    --popover-foreground: 0 0% 5%;

    --primary: 0 0% 15%;

    --primary-foreground: 0 0% 98%;

    --secondary: 0 0% 96%;

    --secondary-foreground: 0 0% 15%;

    --muted: 0 0% 96%;

    --muted-foreground: 0 0% 45%;

    --accent: 0 0% 96%;

    --accent-foreground: 0 0% 15%;

    --destructive: 0 84.2% 60.2%;

    --destructive-foreground: 210 40% 98%;

    --border: 0 0% 90%;

    --input: 0 0% 90%;

    --ring: 0 0% 60%;

    --chart-1: 12 76% 61%;

    --chart-2: 173 58% 39%;

    --chart-3: 197 37% 24%;

    --chart-4: 43 74% 66%;

    --chart-5: 27 87% 67%;

    --radius: 0.5rem
  ;

    --sidebar-background: 0 0% 98%;

    --sidebar-foreground: 0 0% 25%;

    --sidebar-primary: 0 0% 15%;

    --sidebar-primary-foreground: 0 0% 98%;

    --sidebar-accent: 0 0% 95%;

    --sidebar-accent-foreground: 0 0% 15%;

    --sidebar-border: 0 0% 90%;

    --sidebar-ring: 0 0% 60%}
  .dark {

    --background: 0 0% 5%;

    --foreground: 0 0% 98%;

    --card: 0 0% 5%;

    --card-foreground: 0 0% 98%;

    --popover: 0 0% 5%;

    --popover-foreground: 0 0% 98%;

    --primary: 0 0% 85%;

    --primary-foreground: 0 0% 15%;

    --secondary: 0 0% 15%;

    --secondary-foreground: 0 0% 98%;

    --muted: 0 0% 15%;

    --muted-foreground: 0 0% 65%;

    --accent: 0 0% 15%;

    --accent-foreground: 0 0% 98%;

    --destructive: 0 62.8% 30.6%;

    --destructive-foreground: 210 40% 98%;

    --border: 0 0% 20%;

    --input: 0 0% 20%;

    --ring: 0 0% 70%;

    --chart-1: 220 70% 50%;

    --chart-2: 160 60% 45%;

    --chart-3: 30 80% 55%;

    --chart-4: 280 65% 60%;

    --chart-5: 340 75% 55%
  ;

    --sidebar-background: 0 0% 8%;

    --sidebar-foreground: 0 0% 95%;

    --sidebar-primary: 0 0% 85%;

    --sidebar-primary-foreground: 0 0% 15%;

    --sidebar-accent: 0 0% 20%;

    --sidebar-accent-foreground: 0 0% 90%;

    --sidebar-border: 0 0% 25%;

    --sidebar-ring: 0 0% 60%}
}



@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Custom greytone theme enhancements */
@layer components {
  /* Subtle active states with greytone */
  .sidebar-active {
    @apply bg-gray-100 text-gray-900 border-l-2 border-gray-400;
  }

  .dark .sidebar-active {
    @apply bg-gray-800/60 text-gray-100 border-l-2 border-gray-500;
  }

  /* Subtle hover states */
  .sidebar-hover {
    @apply hover:bg-gray-50;
  }

  .dark .sidebar-hover {
    @apply hover:bg-gray-800/30;
  }

  /* Enhanced card styling for both themes */
  .card-enhanced {
    @apply bg-white/80 border-gray-200 shadow-sm;
  }

  .dark .card-enhanced {
    @apply bg-gray-900/60 border-gray-700 shadow-lg;
  }

  /* Refined button styling */
  .btn-primary {
    @apply bg-gray-900 hover:bg-gray-800 text-white;
  }

  .dark .btn-primary {
    @apply bg-gray-100 hover:bg-gray-200 text-gray-900;
  }

  /* Hide scrollbars while maintaining scroll functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }
}
